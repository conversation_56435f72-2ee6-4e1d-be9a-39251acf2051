package com.paic.ncbs.claim.service.user.impl;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.service.user.DepartmentService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service("userInfoService")
public class UserInfoServiceImpl implements UserInfoService {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CacheService cacheService;

    @Override
    public List<DepartmentDTO> getLevel2DeptListExcludeUser(String userId) throws GlobalBusinessException {
        List<DepartmentDTO> allList = departmentService.getDepartmentList();
        List<DepartmentDTO> myList = this.getUserLevel2HeadDeptList(userId);

        List<DepartmentDTO> result = new ArrayList<>();
        if (myList.isEmpty()) {
            return allList;
        } else {
            DepartmentDTO my = myList.get(0);
            for (DepartmentDTO all : allList) {
                if (!all.getCode().equals(my.getCode())) {
                    result.add(all);
                }
            }
        }

        return result;
    }

    @Override
    public List<DepartmentDTO> getUserLevel2HeadDeptList(String userId) throws GlobalBusinessException {
        List<DepartmentDTO> userLevel2HeadDeptList = new ArrayList<>();
        List<DepartmentDTO> departmentList = this.getUserDepartmentList(userId);

        for (DepartmentDTO departmentDTO : departmentList) {
            int level = departmentDTO.getLevel();

            if (ConfigConstValues.HQ_DEPARTMENT.equals(departmentDTO.getCode())
                    || ConfigConstValues.DEPT_LEVEL_TWO == level) {
                userLevel2HeadDeptList.add(departmentDTO);
            }
        }

        return userLevel2HeadDeptList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<DepartmentDTO> getUserDepartmentList(String userId) throws GlobalBusinessException {
        List<DepartmentDTO> deptList = new ArrayList<>();
        try {
            UserInfoDTO u = getUserInfoDTO(userId);
            deptList.add(departmentService.queryDepartmentInfoByDeptCode(u.getComCode()));
        } catch (Exception e) {
            LogUtil.info("查询用户的可操作机构 " + userId);
            throw new GlobalBusinessException(ErrorCode.SystemManage.ERROR_UM_CALL, e);
        }
        return deptList;
    }

    @Override
    public String getUserNameById(String userId) {
        return getUserInfoDTO(userId).getUserName();
    }

    @Override
    public UserInfoDTO getUserInfoDTO(String userCode){
        UserInfoDTO u = null;
        try {
            if(StringUtils.isEmptyStr(userCode) || ConstValues.SYSTEM.equalsIgnoreCase(userCode)){
                u = new UserInfoDTO();
                u.setUserCode(ConstValues.SYSTEM);
                u.setUserName(ConstValues.SYSTEM);
                u.setComCode(ConfigConstValues.HQ_DEPARTMENT);
                return u;
            }
            u = Optional.ofNullable(cacheService.queryUserInfo(userCode)).orElse(new UserInfoDTO());
        }catch (NcbsException e){
            throw new GlobalBusinessException(ErrorCode.CALL_INTERACTED_SYSTEM_ERROR,"查询用户失败");
        }
        return u;
    }

}
