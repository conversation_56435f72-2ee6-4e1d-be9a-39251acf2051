package com.paic.ncbs.claim.service.investigate.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.investigate.*;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.claim.model.vo.investigate.*;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.file.service.FileCommonService;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("investigateTaskService")
public class InvestigateTaskServiceImpl implements InvestigateTaskService {

	@Autowired
    BpmService bpmService;

	@Autowired
	private InvestigateTaskMapper investigateTaskDao;

	@Autowired
	private InvestigateProcessMapper investigateProcessDao;

	@Autowired
	private InvestigateTaskAuditMapper investigateTaskAuditDao;

	@Autowired
	private InvestigateAuditMapper investigateAuditDao;

	@Autowired
	private InvestigateMapper investigateDao;

	@Autowired
	private UserInfoService userInfoService ;

	@Autowired
	private TaskInfoService taskInfoService;

	@Autowired
	private CaseProcessService caseProcessService ;
	
    @Autowired
    private FileCommonService fileCommonService;

	@Autowired
	private IOperationRecordService operationRecordService;

	@Autowired
	private InvestigateService investigateService;

	@Autowired
	private TaskPoolService taskPoolService;

	@Autowired
	private DepartmentDefineMapper departmentDefineMapper;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void modifyInvestigateTask(InvestigateTaskDTO investigateTask) {
		investigateTaskDao.modifyInvestigateTask(investigateTask);
	}


	@Override
	public InvestigateTaskDTO getInvestigateTaskById(String idAhcsInvestigateTask) {
		return investigateTaskDao.getInvestigateTaskById(idAhcsInvestigateTask);
	}


	@Override
	public InvestigateTaskVO getInvestigateTaskLinkedByTaskId(
			String idAhcsInvestigateTask) {
		InvestigateTaskVO vo = investigateTaskDao.getInvestigateTaskLinkedByTaskId(idAhcsInvestigateTask);


		if (null != vo) {
			InvestigateAuditVO investigateAuditVO = investigateAuditDao.getInvestigateAuditByTaskId(vo.getIdAhcsInvestigateTask());

			if (null != investigateAuditVO) {
				UserDTO localMajorManage = investigateTaskDao.getLocalMajorManageByInvestigateId(vo.getIdAhcsInvestigate());
				investigateAuditVO.setInitiatorUm(userInfoService.getUserNameById(localMajorManage.getUserId()) + "-" + localMajorManage.getUserId());
				investigateAuditVO.setInitiatorUmName(localMajorManage.getUserId());
			}
			if (!StringUtils.isEmptyStr(vo.getDispatchUm())){
				vo.setDispatchName(userInfoService.getUserNameById(vo.getDispatchUm()));
			}
			vo.setInvestigateAuditVO(investigateAuditVO);
		}

		if (null != vo && null != vo.getInvestigateAssistVOs() && vo.getInvestigateAssistVOs().size() > 0) {
			for (InvestigateAssistVO assist : vo.getInvestigateAssistVOs()) {
				if (StringUtils.isEmptyStr(assist.getAssistReply())) {
					vo.setIdAhcsInvestigateAssist(assist.getIdAhcsInvestigateAssist());
					break;
				}
			}
		}
		return vo;
	}


	@Override
	public List<InvestigateTaskVO> getInvestigateTaskByInvestigateId(
			String idAhcsInvestigate) throws GlobalBusinessException {
		List<InvestigateTaskVO> taskVOs = investigateTaskDao.getInvestigateTaskByInvestigateId(idAhcsInvestigate);

		List<InvestigateAuditVO> auditVOs = investigateAuditDao.getInvestigateAuditByInvestigateId(idAhcsInvestigate);


		for (InvestigateAuditVO audit : auditVOs) {
			InvestigateTaskVO task = new InvestigateTaskVO();
			task.setCreatedDate(audit.getCreatedDate());
			task.setIsPrimaryTask(InvestigateConstants.VALIDATE_FLAG_NO);
			task.setIsOffsiteTask(InvestigateConstants.VALIDATE_FLAG_YES);
			task.setInvestigateDepartment(audit.getInvestigateDepartment());
			task.setInvestigateDepartmentName(audit.getInvestigateDepartmentName());
			task.setDispatchUm(audit.getInitiatorUm());
			task.setDispatchUmName(audit.getInitiatorUmName());
			task.setInvestigatorUm(audit.getAuditorUm());
			task.setInvestigatorUmName(audit.getAuditorUmName());
			task.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_APPROVAL);
			task.setSurveyType(audit.getSurveyType());
			task.setCompanyName(audit.getCompanyName());
			taskVOs.add(task);
		}


		DecimalFormat df = new DecimalFormat("0.0");
		for (InvestigateTaskVO vo : taskVOs) {
			long end = System.currentTimeMillis();

			if (InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_FINISH.equals(vo.getTaskStatus())) {
				end = vo.getUpdatedDate().getTime();
			}
			long start = vo.getCreatedDate().getTime();
			float  num = (float )(end - start) / 86400000;
			String result = df.format(num);
			vo.setAging(result);
			String surveyType = vo.getSurveyType();
			if(StringUtils.isNotEmpty(surveyType)){
				String surveyTypeDesc = null;
				switch (surveyType){
					case "01" :{
						surveyTypeDesc="内部调查";
						break;
					}
					case "02" :{
						surveyTypeDesc="外部调查-"+vo.getCompanyName();
						break;
					}
					default:
						break;
				}
				vo.setSurveyTypeDesc(surveyTypeDesc);
			}
		}

		return taskVOs;
	}


	@Override
	public InvestigateTaskVOForReport getMajorInvestigateTaskLinkedByInvestigateId(
			String idAhcsInvestigate) {
		InvestigateTaskVOForReport result = new InvestigateTaskVOForReport();

		List<InvestigateProcessVO> processVOs = investigateProcessDao.getInvestigateProcessAssistByInvestigateId(idAhcsInvestigate);
		InvestigateTaskVO vo = investigateTaskDao.getMajorInvestigateTaskLinkedByInvestigateId(idAhcsInvestigate);
		if (null == vo) {
			return null;
		}
		BeanUtils.copyProperties(vo, result);
		result.setInvestigateProcessVOsForAssist(processVOs);
		return result;
	}


	@Override
	public List<InvestigateTaskVO> getAssistInvestigateTaskAllByMajorTaskId(String idAhcsInvestigateTask, String isOffsiteTask) {
		List<InvestigateTaskVO> vos =  investigateTaskDao.getAssistInvestigateTaskAllByMajorTaskId(idAhcsInvestigateTask, isOffsiteTask);
		for (InvestigateTaskVO vo : vos) {
			if (!vo.getTaskStatus().equals(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_FINISH)) {
				vo.setUpdatedDate(null);
			}
		}
		return vos;
	}



	@Override
	@Transactional(rollbackFor = Exception.class)
	public void finishInvestigateTask(InvestigateTaskDTO taskDTO, String userId) throws GlobalBusinessException {
		LogUtil.audit("完成调查任务处理开始。InvestigateTaskDTO = {" + taskDTO + "}");

		if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getHasEvidence())) {
			FileInfoDTO fileInfoDTO = new FileInfoDTO();
			fileInfoDTO.setReportNo(taskDTO.getReportNo());
			fileInfoDTO.setCaseTimes(taskDTO.getCaseTimes());
			fileInfoDTO.setSmallCode("002");
		}

		String taskAuditId = investigateTaskAuditDao.getTaskAuditIdByTaskId(taskDTO.getIdAhcsInvestigateTask());
		if (StringUtils.isNotEmpty(taskAuditId)) {
			return;
		}
		String auditId = "";
		if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsPrimaryTask())) {
			boolean isFinish = this.checkIsFinishTask(taskDTO);
			InvestigateDTO investigateDTO = new InvestigateDTO();
			investigateDTO.setIdAhcsInvestigate(taskDTO.getIdAhcsInvestigate());
			if (isFinish) {
				InvestigateTaskAuditDTO dto = new InvestigateTaskAuditDTO();
				dto.setCreatedBy(userId);
				dto.setIdAhcsInvestigateTask(taskDTO.getIdAhcsInvestigateTask());
				dto.setIdAhcsInvestigateTaskAudit(UuidUtil.getUUID());
				dto.setInitiatorUm(taskDTO.getInvestigatorUm());
				dto.setReviewUserUm(taskDTO.getDispatchUm());
				dto.setUpdatedBy(userId);
				dto.setCommonEstimateFee(taskDTO.getCommonEstimateFee());
				investigateTaskAuditDao.addTaskAudit(dto);
				auditId = dto.getIdAhcsInvestigateTaskAudit();
				taskDTO.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_AUDIT);

				//更新整个调查的状态
				investigateDTO.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_AUDIT);
				investigateDao.modifyInvestigate(investigateDTO);

			} else {
				throw new GlobalBusinessException(ErrorCode.Investigate.HAS_INVESTIGATION_UNFINISHED);
			}
		} else if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsOffsiteTask())) {
			InvestigateTaskAuditDTO dto = new InvestigateTaskAuditDTO();
			dto.setCreatedBy(userId);
			dto.setIdAhcsInvestigateTask(taskDTO.getIdAhcsInvestigateTask());
			dto.setIdAhcsInvestigateTaskAudit(UuidUtil.getUUID());
			dto.setInitiatorUm(taskDTO.getInvestigatorUm());
			dto.setReviewUserUm(taskDTO.getDispatchUm());
			dto.setUpdatedBy(userId);
			dto.setCommonEstimateFee(taskDTO.getCommonEstimateFee());
			investigateTaskAuditDao.addTaskAudit(dto);
			taskDTO.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_AUDIT);
		} else {
			taskDTO.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_FINISH);

		}

		taskDTO.setUpdatedBy(userId);
		taskDTO.setFinishDate(new Date());
		taskDTO.setInvestigatorUm(userId);
		investigateTaskDao.modifyInvestigateTask(taskDTO);

		if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsPrimaryTask())) {
			boolean isFinish = this.checkIsFinishTask(taskDTO);
			if (isFinish) {
				this.completeMajorInvestigateTask(taskDTO, userId,auditId);
			}
		} else if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsOffsiteTask())) {

			this.completeNlInvestigateTask(taskDTO, userId);
		} else {
			this.completeAssistInvestigateTask(taskDTO, userId);
		}

	}



	private void completeNlInvestigateTask(InvestigateTaskDTO taskDTO, String userId) throws GlobalBusinessException {
		Map<String, Object> extVariable = new HashMap<>();
		extVariable.put(InvestigateConstants.ID_AHCS_INVESTIGATE_TASK, taskDTO.getIdAhcsInvestigateTask());
		extVariable.put(InvestigateConstants.DEPARTMENT_CODE, taskDTO.getInvestigateDepartment());
		extVariable.put(InvestigateConstants.DISPATCH_UM, taskDTO.getDispatchUm());
		extVariable.put(InvestigateConstants.ID_AHCS_INVESTIGATE, taskDTO.getIdAhcsInvestigate());
		extVariable.put(InvestigateConstants.INVESTIGATE_NODE, "待审核");

	}


	private void completeAssistInvestigateTask(InvestigateTaskDTO taskDTO, String userId) throws GlobalBusinessException {
		Map<String, Object> extVariable = new HashMap<>();
		extVariable.put(InvestigateConstants.ID_AHCS_INVESTIGATE_TASK, taskDTO.getIdAhcsInvestigateTask());

	}


	private boolean checkIsFinishTask(InvestigateTaskDTO taskDTO) {

		int count = investigateTaskDao.getCountUnfinishedTaskByIdAhcsInvestigate(taskDTO.getIdAhcsInvestigate());
		if (count > 1) {
			return false;
		}


		int count2 = investigateTaskDao.getCountNlAuditingByIdAhcsInvestigate(taskDTO.getIdAhcsInvestigate());
		return count2 <= 0;
	}


	@Transactional(rollbackFor = Exception.class)
	public void completeMajorInvestigateTask(InvestigateTaskDTO taskDTO, String userId,String auditId) throws GlobalBusinessException {
		bpmService.completeTask_oc(taskDTO.getReportNo(),taskDTO.getCaseTimes(),BpmConstants.OC_MAJOR_INVESTIGATE);
		//操作记录
		operationRecordService.insertOperationRecordByLabour(taskDTO.getReportNo(), BpmConstants.OC_MAJOR_INVESTIGATE, "回复", null);
		//操作记录
		operationRecordService.insertOperationRecordByLabour(taskDTO.getReportNo(), BpmConstants.OC_INVESTIGATE_REVIEW, "发起", null);
		bpmService.startProcessOc(taskDTO.getReportNo(),taskDTO.getCaseTimes(),BpmConstants.OC_INVESTIGATE_REVIEW,auditId,null,null);
		caseProcessService.updateCaseProcess(taskDTO.getReportNo(), taskDTO.getCaseTimes(),
				CaseProcessStatus.INVESTIGATION_REVIEW.getCode());

	}


	@Override
	public String getInvestigatorByTaskId(String idAhcsInvestigateTask) throws GlobalBusinessException {
		InvestigateTaskDTO dto =  investigateTaskDao.getInvestigateTaskById(idAhcsInvestigateTask);
		if (null == dto) {
			return null;
		}
		return dto.getInvestigatorUm();
	}

	@Override
	public String getMajorTaskIdByInvestigateId(String idAhcsInvestigate) {
		return investigateTaskDao.getMajorTaskIdByInvestigateId(idAhcsInvestigate);
	}


	@Override
	public Map<String, String> generateInvestigatePdf(String idAhcsInvestigate) {
		String download = null;        
        if(StrUtil.isNotEmpty(idAhcsInvestigate)) {
        	InvestigateTaskDTO investigateTask = investigateTaskDao.getMajorInvestigateTaskByInvestigateId(idAhcsInvestigate);
			if(investigateTask != null && StrUtil.isNotEmpty(investigateTask.getFileId())) {
				try {
					download =  fileCommonService.getDownloadUrl(investigateTask.getFileId(),null, WebServletContext.getUserId());
				} catch (Exception e) {
					throw new GlobalBusinessException("委托调查书生成异常");
				}
			}        	
        }        
		return MapUtil.of("download", download);
	}

	@Override
	public List<UserInfoDTO> getApprovalUsers(String reportNo, Integer caseTimes, String initFlag) throws GlobalBusinessException {
		try {
			// 获取当前部门代码
			String currentDepartmentCode = WebServletContext.getDepartmentCode();

			// 获取审批用户列表
			List<UserInfoDTO> userInfoDTO = getApprovalUsersByDepartment(currentDepartmentCode);

			// 如果是移交审批(initFlag=2)，需要排除之前已经审批过该任务的人
			if ("2".equals(initFlag)) {
				List<String> previouslyApprovedUsers = getPreviouslyApprovedUsers(reportNo, caseTimes);
				userInfoDTO = excludeUsers(userInfoDTO, previouslyApprovedUsers);
			}

			return userInfoDTO;
		} catch (Exception e) {
			throw new GlobalBusinessException("获取审批用户列表失败：" + e.getMessage());
		}
	}

	/**
	 * 根据部门代码获取审批用户列表
	 *
	 * @param departmentCode 部门代码
	 * @return 审批用户列表
	 * @throws GlobalBusinessException 业务异常
	 */
	private List<UserInfoDTO> getApprovalUsersByDepartment(String departmentCode) throws GlobalBusinessException {
		try {
			// 1. 获取调查部门代码
			InvestigateTaskDTO investigateTask = new InvestigateTaskDTO();
			investigateTask.setInvestigateDepartment(Constants.DEPARTMENT_CODE);

			// 获取服务器信息列表
			TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
			List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
			List<String> serverCodeList = new ArrayList<>();

			if (CollectionUtils.isNotEmpty(serverInfoList)) {
				serverCodeList = serverInfoList.stream()
						.map(ServerInfoVO::getServerCode)
						.collect(Collectors.toList());
			}

			// 检查部门代码是否需要替换
			if (StringUtils.isEmptyStr(investigateTask.getInvestigateDepartment())
					|| serverCodeList.contains(investigateTask.getInvestigateDepartment())) {
				investigateTask.setInvestigateDepartment(WebServletContext.getDepartmentCode());
			}

			String investigateDepartment = investigateTask.getInvestigateDepartment();

			// 2. 获取子部门代码列表
			List<String> childCodeList = new ArrayList<>();
			childCodeList.add(investigateDepartment);

			List<String> parentCodeList = new ArrayList<>();
			parentCodeList.add("775");
			childCodeList.addAll(departmentDefineMapper.getChildCodeList(parentCodeList));

			// 3. 获取所有部门的用户信息
			List<UserInfoDTO> userInfoDTO = new ArrayList<>();

			for (String investigateDepartmentCode : childCodeList) {
				String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(investigateDepartmentCode);
				List<UserInfoDTO> departmentUserInfoDTO = taskPoolService.searchTaskDealUser(
						investigateDepartmentCode, BpmConstants.OC_MAJOR_INVESTIGATE);

				// 设置用户的部门信息
				departmentUserInfoDTO.forEach(user -> {
					user.setComCode(investigateDepartmentCode);
					user.setComName(departmentName);
				});

				userInfoDTO.addAll(departmentUserInfoDTO);
			}

			// 4. 构建部门代码筛选列表
			List<String> departmentCodes = new ArrayList<>();
			departmentCodes.add(departmentCode);
			departmentCodes.add(Constants.DEPARTMENT_CODE);

			// 5. 筛选用户
			return userInfoDTO.stream()
					.filter(user -> departmentCodes.contains(user.getComCode()))
					.collect(Collectors.toList());

		} catch (Exception e) {
			throw new GlobalBusinessException("获取审批用户列表失败：" + e.getMessage());
		}
	}

	/**
	 * 从用户列表中排除指定的用户
	 *
	 * @param userInfoDTO 用户信息列表
	 * @param excludeUserCodes 要排除的用户代码列表
	 * @return 筛选后的用户列表
	 */
	private List<UserInfoDTO> excludeUsers(List<UserInfoDTO> userInfoDTO, List<String> excludeUserCodes) {
		if (CollectionUtils.isEmpty(excludeUserCodes)) {
			return userInfoDTO;
		}

		return userInfoDTO.stream()
				.filter(user -> !excludeUserCodes.contains(user.getUserCode()))
				.collect(Collectors.toList());
	}

	/**
	 * 获取之前已经审批过该任务的用户列表
	 * @param reportNo 报案号
	 * @param caseTimes 赔付次数
	 * @return 已审批用户ID列表
	 */
	private List<String> getPreviouslyApprovedUsers(String reportNo, Integer caseTimes) {
		List<String> approvedUsers = new ArrayList<>();
		try {
			// 获取该案件的调查记录
			InvestigateVO investigate = investigateDao.getFirstInvestigate(reportNo, caseTimes);
			if (investigate != null && StringUtils.isNotEmpty(investigate.getAllocateInfo())) {
				String allocateInfo = investigate.getAllocateInfo();
				String[] parts = allocateInfo.split("\\|");

				// 分配人|分配意见|分配时间|已审核人code
				if (parts.length >= 4) {
					String approverCodes = parts[3];
					if (StringUtils.isNotEmpty(approverCodes)) {
						String[] codes = approverCodes.split(",");
						for (String code : codes) {
							if (StringUtils.isNotEmpty(code.trim())) {
								approvedUsers.add(code.trim());
							}
						}
					}
				}
			}
		} catch (Exception e) {
			LogUtil.audit("获取之前已经审批过该任务用户列表失败");
		}
		return approvedUsers.stream().distinct().collect(Collectors.toList());
	}

}