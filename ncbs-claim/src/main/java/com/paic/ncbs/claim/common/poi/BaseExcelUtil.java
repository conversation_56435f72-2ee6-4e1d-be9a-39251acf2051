package com.paic.ncbs.claim.common.poi;


import com.alibaba.excel.EasyExcel;
import com.paic.ncbs.claim.common.annotation.ModelProp;
import com.paic.ncbs.claim.common.annotation.SelectOption;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.vo.batch.BatchAcceptTemplateExcel;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
@Component
public class BaseExcelUtil<T> {

    private static Logger logger = LoggerFactory.getLogger(BaseExcelUtil.class.getName());

    public final static String success = "Excel导出成功";

    public final static String Excel2003 = "2003";

    public final static String Excel2007 = "2007";

    private final static String FIREFOX = "Firefox";

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    private static IOBSFileUploadService iobsFileUploadServiceStatic;

    @PostConstruct
    public void  init (){
        BaseExcelUtil.iobsFileUploadServiceStatic = iobsFileUploadService ;
    }

    public String buildModelSuitIobs(List<T> list,
                                     String[] tableName,
                                     String[] tableFild,
                                     Map<String, String[]> headerCheckList,
                                     String fileName,
                                     String path,
                                     boolean iobs
                                ) throws FileNotFoundException {
        FileOutputStream stream = null;
        try {
            fileName += ".xlsx";
            LogUtil.info("buildModelSuitIobs-iobsTrue:{}",iobs);
            if (iobs){
                LogUtil.info("buildModelSuitIobs-iobs");
                list.stream().forEach((e ->((BatchAcceptTemplateExcel)e).setDepartCode("")));
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream).head(ExcelHelper.batchReportTitleList).sheet("意健险批量报案模板").doWrite(list);
                fileName = iobsFileUploadServiceStatic.uploadFileToFilePlatform(fileName, byteArrayOutputStream.toByteArray());
            }else {
                LogUtil.info("buildModelSuitIobs-notIobs");
                list.stream().forEach((e ->((BatchAcceptTemplateExcel)e).setDepartCode("")));
                try (FileOutputStream fos = new FileOutputStream(FileUploadConstants.LOCAL_PATH+fileName)) {
                    EasyExcel.write(fos).head(ExcelHelper.batchReportTitleList).sheet("意健险批量结案模板").doWrite(list);
                }catch (Exception e){
                    LogUtil.error("生成下载文件失败");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        LogUtil.info("iobsKey:{}",fileName);
        return fileName;
    }


    /**
     * 导出excel2003 <功能详细描述>
     *
     * @param dataList
     * @param tableName
     * @param tableFeild
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private HSSFWorkbook writeXlsData2003(List<T> dataList, String[] tableName, String[] tableFeild) {

        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        if (dataList != null) {
            for (T t : dataList) {
                Map<String, Object> map = new HashMap<String, Object>();
                // BeanUtils.populate(r, map);
                map = this.transBean2Map(t);
                list.add(map);
            }
        }
        List<Map<String, String>> header = new ArrayList<Map<String, String>>();
        for (int i = 0; i < tableName.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", tableName[i]);
            map.put("field", tableFeild[i]);
            header.add(map);
        }

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet();
        HSSFCellStyle style = setCellStyleByWb(wb);
        style.setWrapText(true);
        style.setAlignment(HorizontalAlignment.LEFT);
        HSSFRow row = null;
        // 添加excel头
        row = sheet.createRow(0);
        HSSFCellStyle greenStyle = createGreenStyle(wb);
        if (header != null) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i).get("name") == null ? "" : header.get(i).get("name")
                        .toString());
                cell.setCellStyle(greenStyle);
                sheet.setColumnWidth(i, 4000);
            }
        } else if (list.size() > 0) {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
            }
        }
        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            Cell cell = null;

            for (int j = 0; j < header.size(); j++) {
                cell = row.createCell(j);
                if (header != null) {
                    String value = "";
                    try {
                        value = map.get(header.get(j).get("field")).toString();
                    } catch (Exception e) {
                        value = "";
                        // e.printStackTrace();
                    }

                    cell.setCellValue(value);
                    cell.setCellStyle(style);
                } else {
                    cell.setCellValue(map.get(keys[j].toString()).toString());
                    cell.setCellStyle(style);
                }
            }
        }

        return wb;
    }

    /**
     * @description: 导出2003版本的Excel-用于多个sheet的导出
     * @param wb : 工作簿
     * @param sheetNum : sheet的位置，0表示第一个表格中的第一个sheet
     * @param sheetTitle : sheet的名称
     * @param dataList : 表格-数据
     * @param tableName : 表格头-名称
     * @param tableFeild : 表格头-赋值字段
     */
    public void writeXlsData2003MoreSheet(HSSFWorkbook wb, int sheetNum, String sheetTitle, List<T> dataList, String[] tableName, String[] tableFeild) {
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        if (dataList != null) {
            for (T t : dataList) {
                Map<String, Object> map = new HashMap<String, Object>();
                // BeanUtils.populate(r, map);
                map = this.transBean2Map(t);
                list.add(map);
            }
        }
        List<Map<String, String>> header = new ArrayList<Map<String, String>>();
        for (int i = 0; i < tableName.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", tableName[i]);
            map.put("field", tableFeild[i]);
            header.add(map);
        }

        // 生成一个表格
        HSSFSheet sheet = wb.createSheet();
        wb.setSheetName(sheetNum, sheetTitle);
        //表头样式
        HSSFCellStyle greenStyle = createGreenStyle(wb);
        //单元格样式
        HSSFCellStyle style = setCellStyleByWb(wb);

        // 添加excel头
        HSSFRow row = sheet.createRow(0);
        if (header != null) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i).get("name") == null ? "" : header.get(i).get("name")
                        .toString());
                cell.setCellStyle(greenStyle);
                sheet.setColumnWidth(i, 4000);
            }
        } else if (list.size() > 0) {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
            }
        }

        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            Cell cell = null;

            for (int j = 0; j < header.size(); j++) {
                cell = row.createCell(j);
                if (header != null) {
                    String value = "";
                    try {
                        value = map.get(header.get(j).get("field")).toString();
                    } catch (Exception e) {
                        value = "";
                        // e.printStackTrace();
                    }

                    cell.setCellValue(value);
                    cell.setCellStyle(style);
                } else {
                    cell.setCellValue(map.get(keys[j].toString()).toString());
                    cell.setCellStyle(style);
                }
            }
        }
    }

    private HSSFCellStyle setCellStyleByWb(HSSFWorkbook workbook) {
        HSSFCellStyle hssfCellStyle = workbook.createCellStyle();
        hssfCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        hssfCellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        hssfCellStyle.setBorderTop(BorderStyle.THIN);//上边框
        hssfCellStyle.setBorderRight(BorderStyle.THIN);//右边框
        Font font = workbook.createFont();
        font.setFontName("宋体"); // 字体
        hssfCellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        return hssfCellStyle;

    }



    /**
     * 导出2007
     *
     * @param dataList        导出数据
     * @param tableName       文件头
     * @param tableFeild      对应bean属性
     * @param headerCheckList 下拉列表map<Feild,Value>
     * @param fileName        导出文件名
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private XSSFWorkbook writeXlsxData2007(List<T> dataList, String[] tableName, String[] tableFeild,
                                            Map<String, String[]> headerCheckList, String fileName) throws IOException {

        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        if (tableName == null) {
            return null;
        }
        if (tableFeild == null) {
            return null;
        }
        if (tableName.length != tableFeild.length) {
            return null;
        }

        if (dataList != null) {
            for (T t : dataList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map = this.transBean2Map(t);
                list.add(map);
            }
        }
        XSSFWorkbook wb = new XSSFWorkbook(String.valueOf(500));
        XSSFSheet sheet = wb.createSheet(fileName.split("\\.")[0]);
        sheet.createFreezePane(0,1,0,1);
        sheet.setDefaultRowHeight((short)(5 * 256));
        sheet.setDefaultColumnWidth(17);
        XSSFCellStyle greenStyle = (XSSFCellStyle) createGreenStyle(wb);
        List<Map<String, String>> header = new ArrayList<Map<String, String>>();
        for (int i = 0; i < tableName.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", tableName[i]);
            map.put("field", tableFeild[i]);
            header.add(map);
            if (headerCheckList != null && headerCheckList.containsKey(tableFeild[i])
                    && headerCheckList.get(tableFeild[i]).length < 10) {
                // 添加验证
                DataValidation data_validation_list = this.setDataValidationListNew(sheet,
                        headerCheckList.get(tableFeild[i]), 1, 1000000, i, i);
                // 设置提示内容,标题,内容
                data_validation_list.createPromptBox("提示", "请选择");
                data_validation_list.createErrorBox("错误", "请输入有效值");
                data_validation_list.setEmptyCellAllowed(false);
                data_validation_list.setShowErrorBox(true);
                data_validation_list.setShowPromptBox(true);
                // 工作表添加验证数据
                sheet.addValidationData(data_validation_list);
            } else if (headerCheckList != null && headerCheckList.containsKey(tableFeild[i])) {
                XSSFSheet sheetName = wb.createSheet(tableName[i]);
                // 设置头
                XSSFRow row = sheetName.createRow(0);
                Cell cell1 = row.createCell(0);
                cell1.setCellValue("代码");
                cell1.setCellStyle(greenStyle);
                Cell cell2 = row.createCell(1);
                cell2.setCellValue("名称");
                cell2.setCellStyle(greenStyle);
                String[] nameList = headerCheckList.get(tableFeild[i]);
                for (int j = 0; j < nameList.length; j++) {
                    XSSFRow rowJ = sheetName.createRow(j + 1);
                    Cell cellA = rowJ.createCell(0);
                    if (nameList[j].split("-").length < 1) {
                        continue;
                    }
                    cellA.setCellValue(nameList[j].split("-")[0]);
                    Cell cellB = rowJ.createCell(1);
                    cellB.setCellValue(nameList[j].split("-")[1]);
                }

            }
        }

        CellStyle style = sheet.getWorkbook().createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        XSSFRow row = null;
        // 添加excel头
        row = sheet.createRow(0);

        if (header != null) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i).get("name") == null ? "" : header.get(i).get("name")
                        .toString());
                cell.setCellStyle(greenStyle);
                cell.setCellType(CellType.STRING);
                sheet.setColumnWidth(i, 4000);
            }
        } else if (list.size() > 0) {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
                cell.setCellType(CellType.STRING);
            }
        }

        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            row.setRowStyle(style);
            Cell cell = null;

            for (int j = 0; j < header.size(); j++) {
                if (header != null) {
                    String value = "";
                    try {
                        String key = header.get(j).get("field");
                        if (key.contains("@")) {
                            cell = row.createCell(j, CellType.NUMERIC);
                            value = map.get(key.replace("@", "")).toString();
                            cell.setCellType(CellType.NUMERIC);
                            cell.setCellValue(Double.valueOf(value));
                            cell.setCellStyle(style);
                        } else {
                            cell = row.createCell(j, CellType.STRING);
                            value = map.get(key).toString();
                            cell.setCellValue(value);
                            cell.setCellStyle(style);
                        }
                    } catch (Exception e) {
                        value = "";
                        // e.printStackTrace();
                    }

                } else {
                    cell.setCellValue(map.get(keys[j].toString()).toString());
                    cell.setCellStyle(style);
                }
            }
        }

        return wb;
    }

    /**
     * @description: 导出2007版本的Excel-用于多个sheet的导出
     * @param wb : 工作簿
     * @param sheetNum : sheet的位置，0表示第一个表格中的第一个sheet
     * @param sheetTitle : sheet的名称
     * @param dataList : 表格-数据
     * @param tableName : 表格头-名称
     * @param tableFeild : 表格头-赋值字段
     * @param headerCheckList : 表头对应的下拉列表数据
     * @param fileName : excel文件名
     */
    public void writeXlsxData2007MoreSheet(SXSSFWorkbook wb, int sheetNum, String sheetTitle, List<T> dataList, String[] tableName,
                                           String[] tableFeild, Map<String, String[]> headerCheckList, String fileName) {
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        if (tableName == null) {
            return;
        }
        if (tableFeild == null) {
            return;
        }
        if (tableName.length != tableFeild.length) {
            return;
        }

        if (dataList != null) {
            for (T t : dataList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map = transBean2Map(t);
                list.add(map);
            }
        }

        //wb = new SXSSFWorkbook(500);
        // 生成一个表格
        SXSSFSheet sheet = wb.createSheet(fileName.split("\\.")[0]);
        wb.setSheetName(sheetNum, sheetTitle);
        //表头样式
        XSSFCellStyle greenStyle = (XSSFCellStyle) createGreenStyle(wb);
        List<Map<String, String>> header = new ArrayList<Map<String, String>>();
        for (int i = 0; i < tableName.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("name", tableName[i]);
            map.put("field", tableFeild[i]);
            header.add(map);
            if (headerCheckList != null && headerCheckList.containsKey(tableFeild[i])
                    && headerCheckList.get(tableFeild[i]).length < 10) {
                // 添加验证
                DataValidation data_validation_list = this.setDataValidationList(sheet,
                        headerCheckList.get(tableFeild[i]), 1, 1000000, i, i);
                // 设置提示内容,标题,内容
                data_validation_list.createPromptBox("提示", "请选择");
                data_validation_list.createErrorBox("错误", "请输入有效值");
                data_validation_list.setEmptyCellAllowed(false);
                data_validation_list.setShowErrorBox(true);
                data_validation_list.setShowPromptBox(true);
                // 工作表添加验证数据
                sheet.addValidationData(data_validation_list);
            } else if (headerCheckList != null && headerCheckList.containsKey(tableFeild[i])) {
                SXSSFSheet sheetName = wb.createSheet(tableName[i]);
                // 设置头
                SXSSFRow row = sheetName.createRow(0);
                Cell cell1 = row.createCell(0);
                cell1.setCellValue("代码");
                cell1.setCellStyle(greenStyle);
                Cell cell2 = row.createCell(1);
                cell2.setCellValue("名称");
                cell2.setCellStyle(greenStyle);
                String[] nameList = headerCheckList.get(tableFeild[i]);
                for (int j = 0; j < nameList.length; j++) {
                    SXSSFRow rowJ = sheetName.createRow(j + 1);
                    Cell cellA = rowJ.createCell(0);
                    if (nameList[j].split("-").length < 1) {
                        continue;
                    }
                    cellA.setCellValue(nameList[j].split("-")[0]);
                    Cell cellB = rowJ.createCell(1);
                    cellB.setCellValue(nameList[j].split("-")[1]);
                }

            }
        }

        //单元格样式
        CellStyle style = sheet.getWorkbook().createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        //填充方式
        //style.setFillPattern(CellStyle.SOLID_FOREGROUND);

        // 添加excel头
        SXSSFRow row = row = sheet.createRow(0);
        if (header != null) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i).get("name") == null ? "" : header.get(i).get("name")
                        .toString());
                cell.setCellStyle(greenStyle);
                cell.setCellType(CellType.STRING);
                sheet.setColumnWidth(i, 4000);
            }
        } else if (list.size() > 0) {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
                cell.setCellType(CellType.STRING);
            }
        }

        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            row.setRowStyle(style);
            Cell cell = null;

            for (int j = 0; j < header.size(); j++) {
                //style.setFillBackgroundColor(IndexedColors.WHITE.getIndex()); //默认为白色
                if (header != null) {
                    String value = "";
                    try {
                        String key = header.get(j).get("field");
                        if (key.contains("@")) {
                            cell = row.createCell(j, CellType.NUMERIC);
                            value = map.get(key.replace("@", "")).toString();
                            cell.setCellType(CellType.NUMERIC);
                            cell.setCellValue(Double.valueOf(value));
                            cell.setCellStyle(style);
                        } else {
                            cell = row.createCell(j, CellType.STRING);
                            value = map.get(key).toString();
                            cell.setCellValue(value);
                            /*if("N".equals(value)){
                                style.setFillBackgroundColor(IndexedColors.RED.getIndex()); //为N时背景设置为红色
                            }*/
                            cell.setCellStyle(style);
                        }
                    } catch (Exception e) {
                        value = "";
                        // e.printStackTrace();
                    }

                } else {
                    String value = map.get(keys[j].toString()).toString();
                    cell.setCellValue(value);
                    /*if("N".equals(value)){
                        style.setFillBackgroundColor(IndexedColors.RED.getIndex()); //为N时背景设置为红色
                    }*/
                    cell.setCellStyle(style);
                }
            }
        }
    }

    /**
     * 保存数据到excel文件
     *
     * @param list     数据
     * @param filePath 导保存文件目录
     * @param out      文件输出流
     * @param result   返回结果
     * @param header   excel头
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private Map<String, Object> writeXlsxData(List<Map<String, Object>> list, String filePath,
                                              FileOutputStream out, Map<String, Object> result, ArrayList<String> header) {
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet();

        XSSFRow row = null;
        // 添加excel头
        row = sheet.createRow(0);
        if (header != null && header.size() >= list.get(0).keySet().size()) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i));
            }
        } else {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
            }
        }
        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            Cell cell = null;
            for (int j = 0; j < keys.length; j++) {
                cell = row.createCell(j);
                if (header != null && header.size() >= list.get(0).keySet().size()) {
                    cell.setCellValue(map.get(header.get(j)).toString());
                } else {
                    cell.setCellValue(map.get(keys[j].toString()).toString());
                }
            }
        }

        try {
            wb.write(out);
            wb.close();
            out.flush();
            out.close();

            result.put("code", 1);
            result.put("message", "成功导出" + list.size() + "条记录到" + filePath);
        } catch (Exception e) {
            result.put("code", -200);
            result.put("message", e.getMessage());
        }

        return result;
    }

    /**
     * 保存数据到excel文件
     *
     * @param list     数据
     * @param filePath 导保存文件目录
     * @param out      文件输出流
     * @param result   返回结果
     * @param header   excel头
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private Map<String, Object> writeXlsData(List<Map<String, Object>> list, String filePath,
                                             FileOutputStream out, Map<String, Object> result, ArrayList<String> header) {
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet();

        HSSFRow row = null;
        // 添加excel头
        row = sheet.createRow(0);
        if (header != null && header.size() >= list.get(0).keySet().size()) {
            Cell cell = null;
            for (int i = 0; i < header.size(); i++) {
                cell = row.createCell(i);
                cell.setCellValue(header.get(i));
            }
        } else {
            Cell cell = null;
            Object[] keys = list.get(0).keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(keys[i].toString());
            }
        }
        // 添加excel内容
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            Set<String> set = map.keySet();
            Object[] keys = set.toArray();
            row = sheet.createRow(i + 1);
            Cell cell = null;
            for (int j = 0; j < keys.length; j++) {
                cell = row.createCell(j);
                if (header != null && header.size() >= list.get(0).keySet().size()) {
                    cell.setCellValue(map.get(header.get(j)).toString());
                } else {
                    cell.setCellValue(map.get(keys[j].toString()).toString());
                }
            }
        }

        try {
            wb.write(out);
            wb.close();
            out.flush();
            out.close();

            result.put("code", 1);
            result.put("message", "成功导出" + list.size() + "条记录到" + filePath);
        } catch (Exception e) {
            result.put("code", -200);
            result.put("message", e.getMessage());
        }
        return result;
    }

    /**
     * 对象转换称map
     *
     * @param obj
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static Map<String, Object> transBean2Map(Object obj) {

        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();

                // 过滤class属性
                if (!key.equals("class")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    if (value == null) {
                        continue;
                    }
                    map.put(key, value);
                }

            }
        } catch (Exception e) {
            log.info("transBean2Map Error " + e);
        }

        return map;

    }

    /**
     * 设置样式
     *
     * @param wb
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static CellStyle createGreenStyle(SXSSFWorkbook wb) {
        // 设置字体
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 11); // 字体高度
        font.setFontName("宋体"); // 字体
        font.setBold(true);
//        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 粗体显示

        CellStyle greenStyle = wb.createCellStyle();
        greenStyle.setFillBackgroundColor(FillPatternType.LEAST_DOTS.getCode());
        greenStyle.setFillPattern(FillPatternType.LEAST_DOTS);
        greenStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        greenStyle.setBorderBottom(BorderStyle.MEDIUM);
        greenStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderLeft(BorderStyle.MEDIUM);
        greenStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderRight(BorderStyle.MEDIUM);
        greenStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderTop(BorderStyle.MEDIUM);
        greenStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setFont(font);
        greenStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setWrapText(true);

        return greenStyle;
    }

    /**
     * 设置Excel样式2003
     *
     * @param wb
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static HSSFCellStyle createGreenStyle(HSSFWorkbook wb) {
        // 设置字体
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 11); // 字体高度
        font.setFontName("宋体"); // 字体
        font.setBold(true);
        HSSFCellStyle greenStyle = wb.createCellStyle();
        greenStyle.setFillBackgroundColor(FillPatternType.LEAST_DOTS.getCode());
        greenStyle.setFillPattern(FillPatternType.LEAST_DOTS);
        greenStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        greenStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        greenStyle.setBorderBottom(BorderStyle.MEDIUM);
        greenStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderLeft(BorderStyle.MEDIUM);
        greenStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderRight(BorderStyle.MEDIUM);
        greenStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderTop(BorderStyle.MEDIUM);
        greenStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setFont(font);
        greenStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setWrapText(true);

        return greenStyle;
    }

    /**
     * 设置Excel样式2007
     *
     * @param wb
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private static XSSFCellStyle createGreenStyle(XSSFWorkbook wb) {
        // 设置字体
        XSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) 11); // 字体高度
        font.setFontName("宋体"); // 字体
        font.setBold(true);
        XSSFCellStyle greenStyle = wb.createCellStyle();
        greenStyle.setFillBackgroundColor(FillPatternType.LEAST_DOTS.getCode());
        greenStyle.setFillPattern(FillPatternType.LEAST_DOTS);
        greenStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        greenStyle.setBorderBottom(BorderStyle.MEDIUM);
        greenStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderLeft(BorderStyle.MEDIUM);
        greenStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderRight(BorderStyle.MEDIUM);
        greenStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setBorderTop(BorderStyle.MEDIUM);
        greenStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        greenStyle.setFont(font);
        greenStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        greenStyle.setWrapText(true);

        return greenStyle;
    }

    /**
     * 设置excel数据有效性 <功能详细描述>
     *
     * @param sheet
     * @param firstRow 起始行
     * @param firstCol 终止行
     * @param endRow   起始列
     * @param endCol   终止列
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static DataValidation setDataValidationList(SXSSFSheet sheet, String[] textlist, int firstRow,
                                                        int endRow, int firstCol, int endCol) {
        // 设置下拉列表的内容
        // String[] textlist={"列表1","列表2","列表3","列表4","列表5"};
        // 加载下拉列表内容

        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        constraint.setExplicitListValues(textlist);
        // 设置数据有效性加载在哪个单元格上。

        // 设置数据有效性加载在哪个单元格上。
        // 四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);

        // 数据有效性对象
        DataValidation data_validation = helper.createValidation(constraint, regions);

        return data_validation;
    }

    private static DataValidation setDataValidationListNew(XSSFSheet sheet, String[] textlist, int firstRow,
                                                        int endRow, int firstCol, int endCol) {
        // 设置下拉列表的内容
        // String[] textlist={"列表1","列表2","列表3","列表4","列表5"};
        // 加载下拉列表内容

        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        constraint.setExplicitListValues(textlist);
        // 设置数据有效性加载在哪个单元格上。

        // 设置数据有效性加载在哪个单元格上。
        // 四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);

        // 数据有效性对象
        DataValidation data_validation = helper.createValidation(constraint, regions);

        return data_validation;
    }

    /**
     * 数据转ArrayList
     *
     * @param strs
     * @return
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("unused")
    private ArrayList<String> convertStrs2ArrayList(String[] strs) {
        if (strs == null) {
            return null;
        }
        ArrayList<String> list = new ArrayList<String>();
        list.addAll(Arrays.asList(strs));
        return list;
    }

    private static String getValue(XSSFCell cell) {
        String val = "";
        if (cell == null) {
            return "";
        }
        if (cell.getCellType() == CellType.STRING) {
            val = cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            val = cell.getBooleanCellValue() == true ? "true" : "false";
        } else if (cell.getCellType() == CellType.NUMERIC) {
            //数字格式, 包含日期.进一步处理
            XSSFCellStyle cellStyle = cell.getCellStyle();
            short dataFormat = cellStyle.getDataFormat();
            if (dataFormat <= 22 && dataFormat >= 18 || dataFormat == 14) {
                Date value = cell.getDateCellValue();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                        "yyyy-MM-dd hh:mm:ss");
                String format = simpleDateFormat.format(value);
                val = format;
            } else {
                BigDecimal valtemplete = new BigDecimal(cell.getNumericCellValue() + "");
                if (new BigDecimal(valtemplete.longValue()).compareTo(valtemplete) == 0) {
                    val = valtemplete.longValue() + "";
                } else {
                    val = valtemplete.toString();
                }
            }

        } else {
            cell.setCellType(CellType.STRING);
            val = cell.getStringCellValue();
        }

        return val;
    }

    public static <T> List<T> readFileToWorkbook(InputStream inp, Class<T> cls, int firstRow) throws IOException, InstantiationException, IllegalAccessException {
        try {
            inp = FileMagic.prepareToCheckMagic(inp);
            if (FileMagic.valueOf(inp) == FileMagic.OLE2) {
                logger.info("excel格式：2003及以下");
                return read2003ExcelAsListFromXls(new HSSFWorkbook(inp), cls, firstRow, null);
            }
            if (FileMagic.valueOf(inp) == FileMagic.OOXML) {
                logger.info("excel格式：2007及以上");
                return read2007ExcelAsListFromXls(new XSSFWorkbook(OPCPackage.open(inp)), cls, firstRow, null);
            }
        } catch (IOException | InvalidFormatException e) {
            e.printStackTrace();
        }
        throw new RuntimeException("poi解析excel失败");
    }

    /**
     * 读取文件中数据 <功能详细描述>  主要修改是增加了 Date类型的转换, 会利用poi内置的日期转换功能.
     *
     * @param cls bean类型
     * @see [类、类#方法、类#成员]
     */
    @SuppressWarnings("resource")
    static public <T> List<T> read2003ExcelAsListFromXls(HSSFWorkbook hssfWorkbook, Class<T> cls, int firstRow, String dateFormat) throws IOException, InstantiationException, IllegalAccessException {
        List<T> list = new ArrayList<T>();
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = "yyyy-MM-dd HH:mm:ss";
        }
        // 循环工作表Sheet
        HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
        if (hssfSheet == null) {
            throw new RuntimeException( "导入数据不能为空！");
        }
        if (hssfSheet.getLastRowNum() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }

        Field[] fields = cls.getDeclaredFields();
        List<String> fieldList = new ArrayList<>();
        for (int j = 0; j < fields.length; j++) {
            if (fields[j].isAnnotationPresent(ModelProp.class)) {
                ModelProp modelProp = fields[j].getAnnotation(ModelProp.class);
                if (modelProp.colIndex() != -1) {
                    fieldList.add(modelProp.colIndex(), String.valueOf(fields[j].getName()));
                }
            }
        }

        // 循环行Row-从数据行开始
        for (int rowNum = firstRow; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            T t = cls.newInstance();
            HSSFRow hssfRow = hssfSheet.getRow(rowNum);
            HashMap<String, Object> map = new HashMap<String, Object>();
            // 循环row中的每一个单元格
            if (hssfRow == null) {
                continue;
            }
            for (int i = 0; i < fieldList.size(); i++) {
                HSSFCell cell = hssfRow.getCell(i);
                // 格式转换
                Object val = "";
                if (cell != null) {
                    if (cell.getCellType() == CellType.STRING) {
                        val = cell.getStringCellValue();
                    } else if (cell.getCellType() == CellType.BOOLEAN) {
                        val = cell.getBooleanCellValue() == true ? "true" : "false";
                    } else if (cell.getCellType() == CellType.NUMERIC) {
                        //数字格式, 包含日期.进一步处理
                        HSSFCellStyle cellStyle = cell.getCellStyle();
                        short dataFormat = cellStyle.getDataFormat();
                        if (dataFormat <= 22 && dataFormat >= 18 || dataFormat == 14) {
                            Date value = cell.getDateCellValue();
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
                            String format = simpleDateFormat.format(value);
                            val = format;
                        } else {
                            BigDecimal valtemplete = new BigDecimal(cell.getNumericCellValue() + "");
                            if (new BigDecimal(valtemplete.longValue()).compareTo(valtemplete) == 0) {
                                val = valtemplete.longValue() + "";
                            } else {
                                val = valtemplete.toString();
                            }
                        }

                    } else {
                        cell.setCellType(CellType.STRING);
                        val = cell.getStringCellValue();
                    }
                } else {
                    val = "";
                    checkCellValid(fields, rowNum, i);
                }
                if (i >= fieldList.size()) {
                    //do sth, 这条数据已经超出需要读入的field数目限制了.
                    continue;
                }
                String s = fieldList.get(i);
                if (!s.contains("&")) {
                    map.put(s, cell == null ? "" : val);
                } else {
                    map.put(s.split("&")[0], cell == null ? "" : val);
                }
            }
            transMap2Bean(map, t);

            list.add(t);

        }
        return list;
    }

    static public <T> List<T> read2007ExcelAsListFromXls(XSSFWorkbook xssfWorkbook, Class<T> cls, int firstRow, String dateFormat) throws IOException, InstantiationException, IllegalAccessException {
        List<T> list = new ArrayList<T>();
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = "yyyy-MM-dd HH:mm:ss";
        }
        // 循环工作表Sheet
        XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(0);
        if (xssfSheet == null) {
            throw new RuntimeException( "导入数据不能为空！");
        }
        if (xssfSheet.getLastRowNum() == 0) {
            throw new RuntimeException( "导入数据不能为空！");
        }

        Field[] fields = cls.getDeclaredFields();
        List<String> fieldList = new ArrayList<>();
        for (int j = 0; j < fields.length; j++) {
            if (fields[j].isAnnotationPresent(ModelProp.class)) {
                ModelProp modelProp = fields[j].getAnnotation(ModelProp.class);
                if (modelProp.colIndex() != -1) {
                    fieldList.add(modelProp.colIndex(), String.valueOf(fields[j].getName()));
                }
            }
        }

        // 循环行Row-从数据行开始
        for (int rowNum = firstRow; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {
            T t = cls.newInstance();
            XSSFRow hssfRow = xssfSheet.getRow(rowNum);
            HashMap<String, Object> map = new HashMap<String, Object>();
            // 循环row中的每一个单元格 过滤空行
            if (hssfRow == null) {
                continue;
            } else if (StringUtils.isBlank(getValue(hssfRow.getCell(0))) && StringUtils.isBlank(getValue(hssfRow.getCell(1))) && StringUtils.isBlank(getValue(hssfRow.getCell(3)))) {
                continue;
            }
            for (int i = 0; i < fieldList.size(); i++) {
                XSSFCell cell = hssfRow.getCell(i);
                // 格式转换
                Object val = "";
                if (cell != null) {
                    if (cell.getCellType() == CellType.STRING) {
                        val = cell.getStringCellValue();
                    } else if (cell.getCellType() == CellType.BOOLEAN) {
                        val = cell.getBooleanCellValue() == true ? "true" : "false";
                    } else if (cell.getCellType() == CellType.NUMERIC) {
                        //数字格式, 包含日期.进一步处理
                        XSSFCellStyle cellStyle = cell.getCellStyle();
                        short dataFormat = cellStyle.getDataFormat();
                        if (dataFormat <= 22 && dataFormat >= 18 || dataFormat == 14) {
                            Date value = cell.getDateCellValue();
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
                            String format = simpleDateFormat.format(value);
                            val = format;
                        } else if(HSSFDateUtil.isCellDateFormatted(cell)){
                            //用于转化为日期格式 \
                            Date d = cell.getDateCellValue();
                            DateFormat formater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            val = formater.format(d);
                        } else {
                            BigDecimal valtemplete = new BigDecimal(cell.getNumericCellValue() + "");
                            if (new BigDecimal(valtemplete.longValue()).compareTo(valtemplete) == 0) {
                                val = valtemplete.longValue() + "";
                            } else {
                                val = valtemplete.toString();
                            }
                        }
                    } else {
                        cell.setCellType(CellType.STRING);
                        val = cell.getStringCellValue();
                    }
                    if (val == null || val == "") {
                        checkCellValid(fields, rowNum, i);
                    }
                } else {
                    val = "";
                    checkCellValid(fields, rowNum, i);
                }
                if (i >= fieldList.size()) {
                    //do sth, 这条数据已经超出需要读入的field数目限制了.
                    continue;
                }
                String s = fieldList.get(i);
                if (!s.contains("&")) {
                    map.put(s, cell == null ? "" : val);
                } else {
                    map.put(s.split("&")[0], cell == null ? "" : val);
                }
            }
            transMap2Bean(map, t);

            list.add(t);

        }
        return list;
    }

    private static void checkCellValid(Field[] fields, int rowNum, int i) {
        for (int j = 0; j < fields.length; j++) {
            if (fields[j].isAnnotationPresent(ModelProp.class)) {
                ModelProp modelProp = fields[j].getAnnotation(ModelProp.class);
                if (!modelProp.nullable() && modelProp.colIndex() == i) {
                    String nullError = "第【" + (1 + rowNum) + "】行的【" + modelProp.name() + "】不能为空";
                    throw new RuntimeException( nullError);
                }
            }
        }
    }

    /**
     * map转对象
     *
     * @param map
     * @param obj
     * @see [类、类#方法、类#成员]
     */
    private static void transMap2Bean(Map<String, Object> map, Object obj) {

        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();

            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();

                if (map.containsKey(key)) {
                    Object value = map.get(key);
                    // 得到property对应的setter方法
                    Method setter = property.getWriteMethod();
                    String type = property.getPropertyType().toString();

                    if (type.contains("String")) {
                        SelectOption selectOptionAnno = property.getReadMethod().getAnnotation(SelectOption.class);
                        if (selectOptionAnno != null) {
                            int ind = ArrayUtils.indexOf(selectOptionAnno.values(), value + "");
                            setter.invoke(obj, selectOptionAnno.keys()[ind]);
                        } else {
                            setter.invoke(obj, value.toString());
                        }
                    } else if (type.contains("BigDecimal")) {
                        setter.invoke(obj, new BigDecimal(value.toString()));
                    } else if (type.contains("Integer")) {
                        setter.invoke(obj, (int) Double.parseDouble(value.toString()));
                    } else if (type.contains("long") || type.contains("Long")) {
                        BigDecimal bd;
                        try {
                            bd = new BigDecimal(value.toString());
                            setter.invoke(obj, Long.parseLong(bd.toPlainString()));
                        } catch (Exception e) {
                            setter.invoke(obj, Long.MIN_VALUE);
                        }
                        //补充：Interger 类型的List 并不是int 类型
                    } else if (type.contains("int") && !type.contains("List")) {
                        if (value instanceof String) {
                            String val ="" ;
                            try {
                                val = ((String) value).split("\\.")[0];
                                setter.invoke(obj, Integer.parseInt(val));
                            } catch (Exception e) {
                                setter.invoke(obj, Long.MIN_VALUE);
                            }
                        }
                    } else if (type.contains("Date")) {
                        if (value == null) {
                            setter.invoke(obj, null);
                        } else if (value.getClass().toString().contains("String")) {
                            setter.invoke(obj, null);
                        } else {
                            try {
                                Date date = (Date) value;
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                                        "yyyy-MM-dd hh:mm:ss");
                                String format = simpleDateFormat.format(value);
                                setter.invoke(obj, format);
                            } catch (Exception e) {
                                logger.info("excel parse error ");
                            }
                        }
                    } else {
                        setter.invoke(obj, value);
                    }
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return;

    }

}
