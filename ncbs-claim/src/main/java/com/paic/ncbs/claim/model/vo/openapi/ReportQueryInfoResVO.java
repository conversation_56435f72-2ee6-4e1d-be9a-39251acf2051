package com.paic.ncbs.claim.model.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("理赔案件返回对象")
@Data
public class ReportQueryInfoResVO {

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty(value = "案件状态", notes = "1：在途；2：结案")
    private String caseStatus;
}
