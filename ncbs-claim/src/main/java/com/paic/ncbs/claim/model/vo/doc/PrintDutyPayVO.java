package com.paic.ncbs.claim.model.vo.doc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
//y
@ApiModel("赔付通知书信息VO-CaseRegisterApplyVO")
public class PrintDutyPayVO {

	@ApiModelProperty("持有人姓名")
	private String holderName;

	@ApiModelProperty("保单号")
	private String policyNo;

	@ApiModelProperty("责任名称/保单责任")
	private String dutyName;

	@ApiModelProperty("责任金额")
	private BigDecimal dutyAmount;

	@ApiModelProperty("调整文本区域")
	private String adjustmentTextArea;

	@ApiModelProperty("案件号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private Integer caseTimes;

	@ApiModelProperty("责任明细列表")
	private List<PrintDutyVO> detailArr;

	@ApiModelProperty("拒赔id")
	private String idClmRefusePayment;

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getDutyName() {
		return dutyName;
	}

	public void setDutyName(String dutyName) {
		this.dutyName = dutyName;
	}

	public BigDecimal getDutyAmount() {
		return dutyAmount;
	}

	public void setDutyAmount(BigDecimal dutyAmount) {
		this.dutyAmount = dutyAmount;
	}

	public String getAdjustmentTextArea() {
		return adjustmentTextArea;
	}

	public void setAdjustmentTextArea(String adjustmentTextArea) {
		this.adjustmentTextArea = adjustmentTextArea;
	}

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public Integer getCaseTimes() {
		return caseTimes;
	}

	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}

	public List<PrintDutyVO> getDetailArr() {
		return detailArr;
	}

	public void setDetailArr(List<PrintDutyVO> detailArr) {
		this.detailArr = detailArr;
	}

	public String getIdClmRefusePayment() {
		return idClmRefusePayment;
	}

	public void setIdClmRefusePayment(String idClmRefusePayment) {
		this.idClmRefusePayment = idClmRefusePayment;
	}
}
