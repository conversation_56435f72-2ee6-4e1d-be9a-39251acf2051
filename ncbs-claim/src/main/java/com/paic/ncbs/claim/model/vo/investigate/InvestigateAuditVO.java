package com.paic.ncbs.claim.model.vo.investigate;



import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@SuppressWarnings("serial")
@ApiModel(description = "调查审批表VO")
public class InvestigateAuditVO extends InvestigateAuditDTO {

	@ApiModelProperty(value = "发起人姓名")
    private String initiatorUmName;

	@ApiModelProperty(value = "审批人姓名")
    private String auditorUmName;

	@ApiModelProperty(value = "调查机构名")
    private String investigateDepartmentName;

	public String getInitiatorUmName() {
		return initiatorUmName;
	}

	public void setInitiatorUmName(String initiatorUmName) {
		this.initiatorUmName = initiatorUmName;
	}

	public String getAuditorUmName() {
		return auditorUmName;
	}

	public void setAuditorUmName(String auditorUmName) {
		this.auditorUmName = auditorUmName;
	}

	public String getInvestigateDepartmentName() {
		return investigateDepartmentName;
	}

	public void setInvestigateDepartmentName(String investigateDepartmentName) {
		this.investigateDepartmentName = investigateDepartmentName;
	}
    

}
