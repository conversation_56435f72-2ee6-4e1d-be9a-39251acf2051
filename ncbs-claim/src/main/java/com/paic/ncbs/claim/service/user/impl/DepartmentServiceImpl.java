package com.paic.ncbs.claim.service.user.impl;

import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.service.user.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DepartmentServiceImpl implements DepartmentService {

	@Autowired
	private DepartmentDefineMapper departmentDefineMapper;

	@Override
	public DepartmentDTO queryDepartmentInfoByDeptCode(String departmentCode) {
		RapeStringUtils.checkIsEmpty(departmentCode, "机构编码");
		DepartmentDTO departmentDTO = departmentDefineMapper.queryDepartmentInfoByDeptCode(departmentCode);
		if (departmentDTO == null) {
			departmentDTO = new DepartmentDTO();
		}
		return departmentDTO;
	}

	@Override
	public List<DepartmentDTO> getDepartmentList() {
		return departmentDefineMapper.getDepartmentList();
	}

}
